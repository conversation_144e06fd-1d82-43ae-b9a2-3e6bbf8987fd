it appears that the backend is trying to download a blob that doesn't exist, it says it's looking for 
tranquil-bison-465923-v9-vidcompressor-output-dev/output/2025/07/23/ACgWe6Nz79_gorNx8ZHh3J-_1sBnSWBrERt1ahI7cg9-NKu4RwmMoMmoUio7bC7KSJkPtvB3WI3jj-FsDhTw_Qq1wrOrlKxSJw_20250723_011134_medium_20250723_011137.mp4
whereas i can see the real file in storage at
tranquil-bison-465923-v9-vidcompressor-output-dev/output/2025/07/23/video_20250723_011137.mp4
the actual name is coming from what im setting as the muxstream key so I'm not sure where it's getting the other name