import React, { useEffect, useRef } from 'react';

interface GoogleOneTapProps {
  onSuccess: (credential: string) => void;
  onError?: (error: any) => void;
  disabled?: boolean;
}

declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          prompt: (callback?: (notification: any) => void) => void;
          cancel: () => void;
          disableAutoSelect: () => void;
        };
      };
    };
  }
}

const GoogleOneTap: React.FC<GoogleOneTapProps> = ({ onSuccess, onError, disabled = false }) => {
  const hasInitialized = useRef(false);
  const isPromptShown = useRef(false);

  useEffect(() => {
    // Don't initialize if disabled or already initialized
    if (disabled || hasInitialized.current) {
      return;
    }

    const initializeOneTap = () => {
      if (!window.google?.accounts?.id) {
        console.log('Google Identity Services not loaded yet, retrying...');
        setTimeout(initializeOneTap, 100);
        return;
      }

      try {
        // Get client ID from environment or use the one from backend config
        const clientId = process.env.REACT_APP_GOOGLE_CLIENT_ID || '************-oudag9d2btbee2n0m3ulh9c9pa5dr7fq.apps.googleusercontent.com';

        console.log('Initializing Google One Tap...');

        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: (response: any) => {
            console.log('Google One Tap response received:', response);
            if (response.credential) {
              onSuccess(response.credential);
            } else if (onError) {
              onError(new Error('No credential received from One Tap'));
            }
          },
          auto_select: true, // Enable auto-select for returning users
          cancel_on_tap_outside: true,
          context: 'signin', // Can be 'signin', 'signup', or 'use'
          ux_mode: 'popup', // Use popup mode for better UX
          itp_support: true, // Enable Intelligent Tracking Prevention support
        });

        hasInitialized.current = true;

        // Add a small delay before showing the prompt to ensure the page is fully loaded
        setTimeout(() => {
          if (!disabled && !isPromptShown.current) {
            // Show the One Tap prompt
            window.google.accounts.id.prompt((notification: any) => {
              console.log('Google One Tap notification:', notification);

              if (notification.isNotDisplayed()) {
                const reason = notification.getNotDisplayedReason();
                console.log('One Tap not displayed:', reason);
                // Common reasons:
                // - browser_not_supported: Browser doesn't support One Tap
                // - invalid_client: Client ID is invalid
                // - missing_client_id: No client ID provided
                // - opt_out_or_no_session: User opted out or no Google session
                // - secure_http_required: HTTPS required
                // - suppressed_by_user: User previously dismissed One Tap
                // - unregistered_origin: Origin not registered in Google Console
                // - unknown_reason: Other reasons

                if (reason === 'opt_out_or_no_session') {
                  console.log('User has no Google session or opted out of One Tap');
                } else if (reason === 'suppressed_by_user') {
                  console.log('User previously dismissed One Tap');
                }
              } else if (notification.isSkippedMoment()) {
                console.log('One Tap skipped:', notification.getSkippedReason());
                // Reasons could be:
                // - auto_cancel: Automatically canceled
                // - user_cancel: User canceled
                // - tap_outside: User tapped outside
                // - issuing_failed: Failed to issue credential
              } else if (notification.isDismissedMoment()) {
                console.log('One Tap dismissed:', notification.getDismissedReason());
                // Reasons could be:
                // - credential_returned: Successfully returned credential
                // - cancel_called: Cancel was called programmatically
                // - flow_restarted: Flow was restarted
              }

              isPromptShown.current = false;
            });

            isPromptShown.current = true;
            console.log('Google One Tap prompt initiated');
          }
        }, 1000); // 1 second delay

      } catch (error) {
        console.error('Error initializing Google One Tap:', error);
        if (onError) {
          onError(error);
        }
      }
    };

    // Start initialization
    initializeOneTap();

    // Cleanup function
    return () => {
      if (window.google?.accounts?.id && isPromptShown.current) {
        try {
          window.google.accounts.id.cancel();
        } catch (error) {
          console.warn('Error canceling Google One Tap:', error);
        }
      }
    };
  }, [disabled, onSuccess, onError]);

  // Cancel One Tap when component becomes disabled
  useEffect(() => {
    if (disabled && window.google?.accounts?.id && isPromptShown.current) {
      try {
        window.google.accounts.id.cancel();
        isPromptShown.current = false;
      } catch (error) {
        console.warn('Error canceling Google One Tap on disable:', error);
      }
    }
  }, [disabled]);

  // This component doesn't render anything visible
  return null;
};

export default GoogleOneTap;
