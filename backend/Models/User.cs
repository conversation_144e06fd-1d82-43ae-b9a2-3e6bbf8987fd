using System.ComponentModel.DataAnnotations;

namespace VidCompressor.Models;

public class User
{
    [Key]
    public required string Id { get; set; }

    [Required]
    public required string Email { get; set; }

    public string? SubscriptionStatus { get; set; } = null!;

    public string? GoogleAccessToken { get; set; }

    public DateTime? GoogleTokenExpiry { get; set; }

    public string? GoogleRefreshToken { get; set; }

    // User preferences
    /// <summary>
    /// Default setting for whether to upload compressed videos back to Google Photos
    /// </summary>
    public bool DefaultUploadToGooglePhotos { get; set; } = true;

    // Navigation properties
    public ICollection<CompressionJob> CompressionJobs { get; set; } = new List<CompressionJob>();
}
